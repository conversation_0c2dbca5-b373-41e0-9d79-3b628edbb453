import { JRequest } from "@/services/index";
import { getLoginCode } from "@/utils/wxSdkUtils/account";

/** Api */
const enum commonApi {
  getOpenId = "/applet/getOpenId",
}

/**
 * @description 获取openid
 */
export async function getOpenId() {
  try {
    const code = await getLoginCode();
    const res = await JRequest.post({
      url: commonApi.getOpenId + `?code=${code}`,
    });
    return res;
  } catch (error) {
    console.error("Error getting OpenID:", error);
    throw error;
  }
}
