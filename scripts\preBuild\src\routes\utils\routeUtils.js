"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPageUrlByRouteName = void 0;
const isUtils_1 = require("@/utils/isUtils");
const maps_1 = require("../maps");
function getPageUrlByRouteName(routeName) {
    if (maps_1.routesMap[routeName] && (0, isUtils_1.isString)(maps_1.routesMap[routeName].path)) {
        return `/${maps_1.routesMap[routeName].path}`;
    }
    else {
        throw new Error(`${routeName}未在map中注册或 path 为空`);
    }
}
exports.getPageUrlByRouteName = getPageUrlByRouteName;
