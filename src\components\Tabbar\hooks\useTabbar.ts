import { RouteName } from "@/routes/enums/routeNameEnum";
import { routesMap } from "@/routes/maps";
// import { listNavigationConfig } from "@/services/api/navigation";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { computed, ref } from "vue";

const navigationPageEnum = {
  /**商城首页 */
  0: "home",
  /** 分类 */
  1: "Cate",
};

export const navigationMap = {
  [navigationPageEnum[0]]: {
    pagePath: routesMap[RouteName.Home].path,
    text: "首页",
    iconPath: "/static/images/tabbar/cate.png",
    selectedIconPath: "/static/images/tabbar/cate-selected.png",
  },
  [navigationPageEnum[1]]: {
    pagePath: routesMap[RouteName.Cate].path,
    text: "分类",
    iconPath: "/static/images/tabbar/home.png",
    selectedIconPath: "/static/images/tabbar/home-selected.png",
  },
};

const defaultNavigation = [
    navigationMap[navigationPageEnum[0]],
    navigationMap[navigationPageEnum[1]]
]

const selectedTabKeyRef = ref(routesMap[RouteName.Home].path);
const navigationListRef = ref([]);
const isHiddenTabbar = ref(false);

export function useTabbar() {
  const systemStore = useSystemStoreWithoutSetup();
  async function loadNavigationConfig() {
    if (!navigationListRef.value.length) {
      let navigationList = [];

      try {
            navigationList = defaultNavigation;

          // const resp = await listNavigationConfig();
          // if (!resp.length) {
          //   navigationList = defaultNavigation;
          // } else {
          //   navigationList = resp.map((item) => {
          //     return {
          //       ...navigationMap[navigationPageEnum[item.pointPage]],
          //       text: item.name,
          //       selectedIconPath: item.iconCheckedPath,
          //       iconPath: item.iconUncheckedPath,
          //       pointPage: item.pointPage,
          //     };
          //   });
          // }
        
      } catch (e) {
        console.log(e);
        navigationList = defaultNavigation;
      }
      try {
        systemStore.setNavigationConfigList(navigationList);
        navigationListRef.value = navigationList;

        if (navigationListRef.value[0].pagePath !== "pages/Home/index") {
          // debugger
          wx.switchTab({ url: `${navigationListRef.value[0].pagePath}` });
        }
      } catch (e) {
        console.log(e);
      }
    }
  }
  function setSelectedTabKey(key: string) {
    selectedTabKeyRef.value = key;
  }
  function setTabbarDisplay(status: boolean) {
    isHiddenTabbar.value = !status;
  }
  const isCheckedVideo = computed(() => {
    return (
      selectedTabKeyRef.value === navigationMap[navigationPageEnum[7]].pagePath
    );
  });

  return {
    loadNavigationConfig,
    selectedTabKeyRef,
    navigationListRef,
    setSelectedTabKey,
    isHiddenTabbar,
    setTabbarDisplay,
    isCheckedVideo,
  };
}
