"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.S = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.S = {
    [routeNameEnum_1.RouteName.Demo]: {
        "path": "subPackages/S/Demo/index",
        "style": {
            "navigationBarTitleText": "内容详情",
            "enablePullDownRefresh": false,
        }
    },
    [routeNameEnum_1.RouteName.SWebView]: {
        "path": "subPackages/S/WebView/index",
        "style": {
            "navigationBarTitleText": "详情",
            "enablePullDownRefresh": false,
        }
    },
    [routeNameEnum_1.RouteName.Check]: {
        "path": "subPackages/S/WebView/Check",
        "style": {
            "navigationBarTitleText": "加载中",
            "enablePullDownRefresh": false,
        }
    },
};
