import dayjs from "dayjs";
/**
 * 时间戳转化日期
 * @param time 时间戳
 * @param perch 占位
 */
export function timeTransform(time: string, perch: string = "-") {
  const date = new Date(time);
  const year = date.getFullYear();
  let month:any = date.getMonth() + 1;
  let day:any = date.getDate();
  if (month < 10) month = "0" + month;
  if (day < 10) day = "0" + day;
  return year + perch + month + perch + day;
}

/**
 * 秒转化为时分秒
 * @param time 秒
 * @returns 时分秒
 */
export function formateTime(time: number): string {
  const h = Math.floor(time / 3600);
  const minute = Math.floor((time / 60) % 60);
  const second = Math.ceil(time % 60);
  const hours = h < 10 ? "0" + h : h;
  const formatSecond = second > 59 ? 59 : second;
  return `${Number(hours) > 0 ? `${hours}时:` : ""}${
    minute < 10 ? "0" + minute : minute
  }分:${formatSecond < 10 ? "0" + formatSecond : formatSecond}秒`;
}

/**
 * 秒转化为分秒和时分秒
 * @param time 秒
 */
export function formateTimeInfo(time: number): { ms: string; hms: string } {
  let hours = zeroFill(Math.floor(time / 3600));
  let minutes = zeroFill(Math.floor(time / 60));
  let _minute = zeroFill(Math.floor((time / 60) % 60));
  let seconds = zeroFill(Math.ceil(time % 60));
  const formatSecond = seconds > 59 ? 59 : seconds;
  function zeroFill(n) {
    return n < 10 ? "0" + n : n;
  }
  return {
    hms: `${hours}小时${_minute}分钟${formatSecond}秒`,
    ms: `${minutes}分钟${formatSecond}秒`,
  };
}

export function createTimeFormat(start:string,end:string){
  let starts = dayjs(start).format("YYYY-MM-DD")
  let ends = dayjs(end).format("YYYY-MM-DD")
  let Timestr = ''
  if(starts == ends){
      let startH = dayjs(start).format("HH:mm")
      let endH = dayjs(end).format("HH:mm")
      Timestr = dayjs(start).format("MM月DD日")+' '+startH+'-'+endH
  }else{
      let startY = dayjs(start).format("YYYY")
      let endY = dayjs(end).format("YYYY")
      if(startY == endY){
          startY = dayjs(start).format("MM月DD日")
          endY = dayjs(end).format("MM月DD日")
          Timestr = startY+' - '+endY
      }else{
        Timestr = start+' - '+end
      }
     
      
  }
  return Timestr
}