import type { Plugin } from "vite";
const fs = require("fs");
const path = require("path");

const Dist_Directory = path.resolve(__dirname, "../../dist/build/mp-weixin");

/**
 * @description 根据路径删除文件
 * @param paths 删除文件路径
 */
export function delFileByPath(paths: string[]): Plugin {
  return {
    name: "vite-plugin-delFile",
    closeBundle() {
      try {
        paths.forEach((relativePath) => {
          const filePath = path.join(Dist_Directory, relativePath);
          if (fs.existsSync(filePath)) {
            fs.rmSync(filePath, { force: true });
          }
          
          // 获取文件所在的目录
          const dirPath = path.dirname(filePath);
          deleteEmptyDirs(dirPath);
        });
      } catch (error) {
        console.error("删除文件失败: ", error);
      }
    },
  };
}

/**
 * @description 删除空文件夹
 * @param dirPath 目录路径
 */
function deleteEmptyDirs(dirPath) {
  if (fs.existsSync(dirPath)) {
    const files = fs.readdirSync(dirPath);
    // 如果目录为空，删除它
    if (files.length === 0) {
      fs.rmdirSync(dirPath);
      // 递归检查并删除上级空目录
      deleteEmptyDirs(path.dirname(dirPath));
    }
  }
}
