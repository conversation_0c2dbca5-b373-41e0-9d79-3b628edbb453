"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Order = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Order = {
    [routeNameEnum_1.RouteName.Order]: {
        "path": "subPackages/Order/index",
        "style": {
            "navigationBarTitleText": "我的订单",
            "enablePullDownRefresh": false
        }
    },
    [routeNameEnum_1.RouteName.OrderDetail]: {
        "path": "subPackages/Order/Details/OrderDetails",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.CancelDetail]: {
        "path": "subPackages/Order/Details/CancelDetail",
        "style": {
            "navigationBarTitleText": "取消订单",
            "enablePullDownRefresh": false,
        }
    },
    [routeNameEnum_1.RouteName.LogisticsDetail]: {
        "path": "subPackages/Order/Details/LogisticsDetail",
        "style": {
            "navigationBarTitleText": "物流信息",
            "enablePullDownRefresh": false,
        }
    },
    [routeNameEnum_1.RouteName.RefundDetails]: {
        "path": "subPackages/Order/Details/RefundDetails",
        "style": {
            "navigationBarTitleText": "申请退款",
            "enablePullDownRefresh": false,
        }
    },
    [routeNameEnum_1.RouteName.AfterSaleDetail]: {
        "path": "subPackages/Order/Details/AfterSaleDetail",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.OrderConfirm]: {
        "path": "subPackages/Order/confirmOrder/index",
        "style": {
            "navigationBarTitleText": "确认订单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.Pay]: {
        "path": "pages/Pay/index",
        "style": {
            "navigationBarTitleText": "收银台",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff",
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.OrderAgent]: {
        "path": "subPackages/PlaceOrder/OrderAgent/index",
        "style": {
            "navigationBarTitleText": "代下单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff",
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.OrderSupplementary]: {
        "path": "subPackages/PlaceOrder/OrderSupplementary/index",
        "style": {
            "navigationBarTitleText": "补充订单信息",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.OrderAgentResult]: {
        "path": "subPackages/PlaceOrder/OrderAgentResult/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
};
