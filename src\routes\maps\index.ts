// const MapsModules = import.meta.glob('./*.map.ts',{ eager: true })
// const MapsObject = {}
// Object.values(MapsModules).forEach((modules)=>{
//   for(let key in modules as object){
//     Object.assign(MapsObject,modules[key])
//   }
// })


// export const routesMap = MapsObject
import {Home} from "./Home"
import {Cate} from "./Cate"
const MapsObject = {
  ...Home,
  ...Cate,
}
export const routesMap = MapsObject
