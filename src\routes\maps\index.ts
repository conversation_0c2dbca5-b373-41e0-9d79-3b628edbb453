// const MapsModules = import.meta.glob('./*.map.ts',{ eager: true })
// const MapsObject = {}
// Object.values(MapsModules).forEach((modules)=>{
//   for(let key in modules as object){
//     Object.assign(MapsObject,modules[key])
//   }
// })


// export const routesMap = MapsObject
import {User} from "./account.map"
import {Cart} from "./cart.map"
import {Cate} from "./cate.map"
import {Independent} from "./independent.map"
import {Order} from "./order.map"
import {Prescription} from "./prescription.map"
import { IntegralHome } from "./integralHome.map"
import { Video } from "./video.map"
import { Distribute } from "./distribute.map"
import { S } from "./s.map"
import { Stream } from "./stream.map";
import { Message } from "./message.map"
import { Inquiry } from "./inquiry.map"
const MapsObject = {
  ...User,
  ...<PERSON><PERSON>,
  ...<PERSON><PERSON>,
  ...Independent,
  ...Order,
  ...Prescription,
  ...IntegralHome,
  ...Video,
  ...Distribute,
  ...S,
  ...Message,
  ...Inquiry,
  ...Stream,
}
export const routesMap = MapsObject
