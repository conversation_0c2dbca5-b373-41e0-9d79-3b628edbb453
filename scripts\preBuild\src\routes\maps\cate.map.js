"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cate = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Cate = {
    [routeNameEnum_1.RouteName.Cate]: {
        path: "pages/Category/index",
        style: {
            "navigationBarTitleText": "健康商城",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            // "componentPlaceholder": {
            // 	"goods-sku-modal":"view"
            // }
        }
    },
    [routeNameEnum_1.RouteName.Therapy]: {
        path: "pages/Therapy/index",
        style: {
            "navigationBarTitleText": "健康疗法",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.GoodsDetail]: {
        "path": "subPackages/GoodsDetail/index",
        "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false,
            // "navigationStyle": "custom"
        }
    },
};
