import type { Plugin } from "vite";

/** 
 * @description 替换导入语句
 * @param code
 * @param regex 替换代码块正则
 * @param largeImageSrcList 大图列表
 * @param prefix 前缀
 */
export function transformImageUrl(
  largeImageSrcList: Array<string>,
  prefix: string
): Plugin {
  return {
    name: "vite-plugin-transform",
    enforce: "pre",
    transform(code, id, opt) {
      // 只处理 src 文件夹下的文件
      if (!id.includes("/src/")) return;

      // 执行所有替换操作
      let transformedCode = replaceImports(code, largeImageSrcList, prefix);

      return transformedCode;
    },
  };
}

/**
 * @description 替换导入语句和图片路径的辅助函数
 * @param code
 * @param regex 替换代码块正则
 * @param largeImageSrcList 大图列表
 * @param prefix 前缀
 */
function replaceImports(code: string, largeImageSrcList: Array<string>, prefix: string): string {
  // 第一种情况: import 语句替换
  const importRegex = /import\s+(\w+)\s+from\s+['"](.+?\.(png|jpe?g|gif|bmp|webp))['"];?/g;
  code = code.replace(importRegex, (match, imgName, importPath) => {
    // const normalizedPath = importPath.match(/@(.+)/)[1];
    // 修复后
const matchs = importPath.match(/@(.+)/);
if (!match) return matchs; // 如果没有匹配到 @ 符号，直接返回原始匹配
const normalizedPath = match[1];
    if (largeImageSrcList.includes(normalizedPath)) {
      return `const ${imgName} = \`${prefix}${normalizedPath}\`;`;
    }
    return match;
  });

  // 第二种情况: <image> 标签替换
  const imageTagRegex = /<image[^>]*\s+src=(['"]?)(\/?(\.?\.\/)+.*?|@\/.*?\.(png|jpe?g|gif|bmp|webp))\1/g;
  code = code.replace(imageTagRegex, (match, quote, imgPath) => {
    const normalizedMatch = imgPath.match(/\/static\/.*?\.(png|jpe?g|gif|bmp|webp)/);
    const normalizedPath = normalizedMatch ? normalizedMatch[0] : null;
    if (normalizedPath && largeImageSrcList.includes(normalizedPath)) {
      return match.replace(imgPath, `${prefix}${normalizedPath}`);
    }
    return match;
  });

  // 第三种情况: 处理 background-image 和 background 属性
  const backgroundRegex = /background(?:-image)?:\s*url\(['"](.+?\.(png|jpe?g|gif|bmp|webp))['"]\)(.*?);/g;
  code = code.replace(backgroundRegex, (match, imgPath, extension, rest) => {
    // 提取 /static 开头的路径
    const normalizedMatch = imgPath.match(/\/static\/.*?\.(png|jpe?g|gif|bmp|webp)/);
    const normalizedPath = normalizedMatch ? normalizedMatch[0] : null;
    if (normalizedPath && largeImageSrcList.includes(normalizedPath)) {
      // 构建新的背景属性字符串
      let newBackground = `background${match.includes('background-image') ? '-image' : ''}: url('${prefix}${normalizedPath}')`;
      // 如果 `rest` 不为空且有效，则保留原有的其他背景属性
      if (rest && rest.trim()) {
        newBackground += ` ${rest.trim()}`;
      }
      // 返回替换后的背景属性字符串
      return `${newBackground};`;
    }
    // 如果不符合条件，保持原有字符串不变
    return match;
  });

  // 第四种情况: 匹配对象或数组中的图片路径 (包括 "@/static" 和 "../../static")
  const objectArrayImageRegex = /['"](@?\/?(\.?\.\/)*static\/.+?\.(png|jpe?g|gif|bmp|webp))['"]/g;
  code = code.replace(objectArrayImageRegex, (match, imgPath) => {
    const normalizedMatch = imgPath.match(/\/static\/.*?\.(png|jpe?g|gif|bmp|webp)/);
    const normalizedPath = normalizedMatch ? normalizedMatch[0] : null;
    if (normalizedPath && largeImageSrcList.includes(normalizedPath)) {
      return match.replace(imgPath, `${prefix}${normalizedPath}`);
    }
    return match;
  });

  return code;
}
