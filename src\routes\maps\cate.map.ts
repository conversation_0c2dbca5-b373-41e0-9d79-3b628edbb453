import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Cate:RoutesMap = {
    [RouteName.Cate]:{
		path: "pages/Category/index",
		style: {
			"navigationBarTitleText": "健康商城",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom",
			// "componentPlaceholder": {
			// 	"goods-sku-modal":"view"
			// }
		}
	},	
    [RouteName.Therapy]:{
		path: "pages/Therapy/index",
		style: {
			"navigationBarTitleText": "健康疗法",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},	
    [RouteName.GoodsDetail]:{
        "path": "subPackages/GoodsDetail/index",
        "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false,
            // "navigationStyle": "custom"
        }
    },
}