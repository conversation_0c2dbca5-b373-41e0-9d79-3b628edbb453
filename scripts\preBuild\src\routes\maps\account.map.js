"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.User = {
    [routeNameEnum_1.RouteName.User]: {
        "path": "pages/User/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.UserSettings]: {
        "path": "subPackages/User/setting/index",
        "style": {
            "navigationBarTitleText": "设置",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserVerify]: {
        "path": "subPackages/User/verify/index",
        "style": {
            "navigationBarTitleText": "身份认证",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserVerifyResult]: {
        "path": "subPackages/User/verify/result",
        "style": {
            "navigationBarTitleText": "身份认证",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserAddress]: {
        "path": "subPackages/User/address/index",
        "style": {
            "navigationBarTitleText": "收货地址",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserAddressEdit]: {
        "path": "subPackages/User/address/addAddress",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserBuyerList]: {
        "path": "subPackages/User/UserBuyerList/index",
        "style": {
            "navigationBarTitleText": "用药人列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.UserBuyerEdit]: {
        "path": "subPackages/User/UserBuyerEdit/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.ListOfMedicalConsultations]: {
        "path": "subPackages/User/ListOfMedicalConsultations/index",
        "style": {
            "navigationBarTitleText": "问诊列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.MedicalConsultationFormDetail]: {
        "path": "subPackages/User/ListOfMedicalConsultations/components/medicalConsultationsDetail",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.MyDoctorList]: {
        "path": "subPackages/User/myDoctorList/index",
        "style": {
            "navigationBarTitleText": "我的医生",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.MyAppointment]: {
        "path": "subPackages/User/myAppointmentList/index",
        "style": {
            "navigationBarTitleText": "我的预约",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.AppointmentForMedicalConsultation]: {
        "path": "subPackages/User/appointmentForMedicalConsultation/index",
        "style": {
            "navigationBarTitleText": "问诊预约单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.Balance]: {
        "path": "subPackages/User/Balance/index",
        "style": {
            "navigationBarTitleText": "账户余额",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
};
