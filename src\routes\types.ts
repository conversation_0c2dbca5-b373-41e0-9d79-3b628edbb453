import type { RouteName } from "./enums/routeNameEnum";

export interface NavigateDefaultParams{
    url:RouteName,
    /** 跳转参数，会转换成键值对拼接在url后面 */
    props?:Record<string,string|number>
    success?:()=>void,
    fail?:()=>void,
    complete?:()=>void,
}
export interface NavigateToParams extends NavigateDefaultParams{
    events?:{},
}
export interface NavigateBackParams{
    delta?:number,
    success?:()=>void,
    fail?:()=>void,
    complete?:()=>void,
}

export interface PageStyle{
    navigationBarBackgroundColor?: string,
    navigationBarTextStyle?: string,
    navigationBarTitleText: string,
    navigationBarShadow?: string,
    navigationStyle?: 'default' | 'custom',
    disableScroll?: string,
    backgroundColor?: string,
    backgroundTextStyle?: 'dark' | 'light',
    enablePullDownRefresh?: boolean,
    onReachBottomDistance?: number,
    pageOrientation?: 'auto ' | 'portrait ' | 'landscape ',
    mpWeixin?: {},
    usingComponents?: {},
    componentPlaceholder?:Record<string,string>
}

export interface RoutePageConfig{
    path:string,
    style?:PageStyle,
    needLogin?:boolean,
}
export type RoutesMap = Partial<Record<RouteName,RoutePageConfig>>
