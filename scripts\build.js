require('module-alias/register');
const { spawn } = require('child_process'); 
const { cleanupByDirectory, coverFileContentByPath, filterLargeImages } = require("./fileUtils.js");
const Prebuild_Directory = './scripts/preBuild';
const Mp_Project_Directory = './src';
/** 过滤图片大小 kb */
const IMAGESIZE = 2;
/** 压缩图片大小 kb */
const COMPRESSSIZEKB = 10;

function execPromise(command){
    return new Promise((resolve,reject)=>{
        const cmdList = command.split(' ')
        console.log('-----------------exec command-----------------')
        console.log(`${command}`)
        console.log('----------------------------------------------')
        const child = spawn(cmdList[0],cmdList.slice(1), { shell: true });
        child.stdout.on('data', (data) => {
            console.log(`${data}`);
        });
        child.stderr.on('data', (data) => {
          console.error(`${data}`);
        });
        child.on('error', (error) => {
            reject(`${command} exec error: ${error}`);
          });

        child.on('close', (code) => {
            resolve(code)
        });
    })
}

exports.build = async(isDev)=>{
    try{
        await cleanupByDirectory(Prebuild_Directory)
        await execPromise('npx tsc --project pre_build_tsconfig.json')
        const {prodConfig} = require("./preBuild/src/config/pagesConfig.js")
        await coverFileContentByPath(`${Mp_Project_Directory}/pages.json`,JSON.stringify(prodConfig))

        await filterLargeImages(`${Mp_Project_Directory}/static/images`, `./large_images/images`, `${Prebuild_Directory}/largeImageSrcList.js`, IMAGESIZE, COMPRESSSIZEKB);

        await execPromise(isDev?'npx uni -p mp-weixin':'npx uni build -p mp-weixin')
    }
    catch(e){
        console.log(`build error: ${e}`);
    }
}

