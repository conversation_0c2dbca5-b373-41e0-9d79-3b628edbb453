import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Order:RoutesMap = {
    [RouteName.Order]:{
        "path": "subPackages/Order/index",
        "style": {
            "navigationBarTitleText": "我的订单",
            "enablePullDownRefresh": false
        }
    },
    [RouteName.OrderDetail]:{
        "path": "subPackages/Order/Details/OrderDetails",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.CancelDetail]:{
        "path": "subPackages/Order/Details/CancelDetail",
        "style": {
            "navigationBarTitleText": "取消订单",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.LogisticsDetail]:{
        "path": "subPackages/Order/Details/LogisticsDetail",
        "style": {
            "navigationBarTitleText": "物流信息",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.RefundDetails]:{
        "path": "subPackages/Order/Details/RefundDetails",
        "style": {
            "navigationBarTitleText": "申请退款",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.AfterSaleDetail]:{
        "path": "subPackages/Order/Details/AfterSaleDetail",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.OrderConfirm]:{
        "path": "subPackages/Order/confirmOrder/index",
        "style": {
            "navigationBarTitleText": "确认订单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.Pay]:{
        "path": "pages/Pay/index",
        "style": {
            "navigationBarTitleText": "收银台",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff",
            "navigationStyle": "custom"
        }
    },
    [RouteName.OrderAgent]:{
        "path": "subPackages/PlaceOrder/OrderAgent/index",
        "style": {
            "navigationBarTitleText": "代下单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff",
            "navigationStyle": "custom"
        }
    },
    [RouteName.OrderSupplementary]:{
        "path": "subPackages/PlaceOrder/OrderSupplementary/index",
        "style": {
            "navigationBarTitleText": "补充订单信息",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.OrderAgentResult]:{
        "path": "subPackages/PlaceOrder/OrderAgentResult/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
}