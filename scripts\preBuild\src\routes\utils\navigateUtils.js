"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.navigateBack = exports.switchTab = exports.reLaunch = exports.redirectTo = exports.navigateTo = void 0;
const isUtils_1 = require("@/utils/isUtils");
const routeUtils_1 = require("./routeUtils");
function urlChangedByProps({ url, props }) {
    let _url = (0, routeUtils_1.getPageUrlByRouteName)(url);
    if ((0, isUtils_1.isObject)(props) && Object.keys(props).length) {
        Object.entries(props).forEach((item, index) => {
            _url = `${_url}${index == 0 ? '?' : '&'}${item.join('=')}`;
        });
    }
    return _url;
}
function navigateTo(params) {
    return uni.navigateTo({
        ...params,
        url: urlChangedByProps(params)
    });
}
exports.navigateTo = navigateTo;
function redirectTo(params) {
    return uni.redirectTo({
        ...params,
        url: urlChangedByProps(params)
    });
}
exports.redirectTo = redirectTo;
function reLaunch(params) {
    return uni.reLaunch({
        ...params,
        url: urlChangedByProps(params)
    });
}
exports.reLaunch = reLaunch;
function switchTab(params) {
    return uni.switchTab({
        ...params,
        url: urlChangedByProps(params)
    });
}
exports.switchTab = switchTab;
function navigateBack(params = {}) {
    const index = getCurrentPages().length;
    if ((index == 1) && !(0, isUtils_1.isFunction)(params.fail)) {
        return Promise.reject('navigateBack fail');
    }
    else {
        return uni.navigateBack({
            ...params,
        });
    }
}
exports.navigateBack = navigateBack;
