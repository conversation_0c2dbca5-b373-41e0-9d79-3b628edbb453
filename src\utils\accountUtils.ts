import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import { accountLogin, getUserPhoneByCode } from "@/services/api/account";
import { clearAllStorage } from "./cache/storage";
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { routesMap } from "@/routes/maps"
import { duplicateNewCopy } from "./commonUtils";


export async function userLogin(loginCode:string):Promise<string>{
    try{
        const resp = await accountLogin({code:loginCode})
        const userStore = useUserInfoStoreWithoutSetup();
        const { gender , nickName , mobile , name , idNo , token } = resp
        if(token){
            const userInfo = {
                gender,
                nickName,
                mobile,
                name,
                idNo
            }
            userStore.setUserInfo(userInfo)
            return token
        }
        else{
            throw new Error('no token return')
        }
    }
    catch(e){
        throw new Error(`${e}`)
    }
}

export async function getUserPhone(phoneCode:string){
    try{
        const userStore = useUserInfoStoreWithoutSetup();
        const mobile =  await getUserPhoneByCode(phoneCode)
        if(mobile){
            const _prevUserinfo =  duplicateNewCopy(userStore.userInfo)
            userStore.setUserInfo({
                ..._prevUserinfo,
                mobile
            })
        }
    }
    catch(e){
        throw new Error(`${e}`)
    }
} 



export function logoutHandler(isRedirect = true){
    /** 如果当前页面已经是login页 不用重复跳转 */
    if( getCurrentPages()[getCurrentPages().length - 1].route == routesMap[RouteName.Inquiry].path ) {
        return
    }
    clearAllStorage()
    const userStore = useUserInfoStoreWithoutSetup()
    userStore.$reset();
    if (isRedirect) {
        uni.showToast({
            title: `当前登录已失效`,
            icon: "none",
        });
        navigateTo({
            url: RouteName.Login,
        }); 
    }
}