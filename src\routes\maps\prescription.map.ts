import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Prescription:RoutesMap = {
    [RouteName.Prescription]:{
        "path": "subPackages/Prescription/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.PrescriptionPlaceOrder]:{
        "path": "subPackages/Prescription/placeOrder",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.PrescriptionForm]:{
        "path": "subPackages/Prescription/prescription",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.PrescriptionList]:{
        "path": "subPackages/User/prescription/index",
        "style": {
            "navigationBarTitleText": "我的处方",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.PrescriptionDetail]:{
        "path": "subPackages/User/prescription/prescrptionDetail",
        "style": {
            "navigationBarTitleText": "处方详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.ListOfPrescriptions]:{
        "path": "subPackages/Prescription/ListOfPrescriptions/index",
        "style": {
            "navigationBarTitleText": "处方单列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.PrescriptionFormDetail]:{
        "path": "subPackages/Prescription/ListOfPrescriptions/components/prescriptionsDetail",
        "style": {
            "navigationBarTitleText": "处方单详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
}