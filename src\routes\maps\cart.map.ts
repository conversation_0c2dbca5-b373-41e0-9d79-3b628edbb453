import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Cart:RoutesMap = {
    [RouteName.Cart]:{
        "path": "pages/Cart/index",
        "style": {
            "navigationBarTitleText": "购物车",
            "enablePullDownRefresh": false
        }
    },
    [RouteName.GoodsDetail]:{
        "path": "pages/Category/goodsDetail/index",
        "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
}
