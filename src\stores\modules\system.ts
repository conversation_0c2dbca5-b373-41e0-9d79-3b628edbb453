import { defineStore } from "pinia";
import { stores } from "@/stores";
import { StoreNamesEnum } from "@/enum/storeNamesEnum";
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/cache/config";
export interface SystemInterface{
    /** 版本 */
    version:string,
    /** 导航配置 */ 
    navigationConfigList?:[],
}
export const systemStore = defineStore(StoreNamesEnum.system, {
    state: ():SystemInterface => {
      return {
        version:'1.0.4',
      };
    },
    actions: {
      setNavigationConfigList(config){
        this.navigationConfigList = config
      },
    },
    getters:{

    }
  });
  
  export function useSystemStoreWithoutSetup() {
    return systemStore(stores);
  }
  