import type { CacheKeysEnum } from "@/enum/cacheKeysEnum";
import {decryption,encryption} from "../crypto"
import { isArray, isNullOrUnDef, isObject } from "../isUtils";
import dayjs from "dayjs";
import type { CacheParams } from "./type";
import { isDevEnv } from "../envUtils";

const ssMap = new Map()
class WebStorage{
  private hasEncrypt: boolean;
  private KEY: CacheKeysEnum;
  private expire: null | number;

  constructor({ hasEncrypt = true, key, expire }:CacheParams) {
    this.hasEncrypt = isDevEnv() ? false : hasEncrypt;
    this.KEY = key;
    this.expire = expire;
  }

  get(objectKey?: string | number){
    const val = uni.getStorageSync(this.KEY);
    if (!val) return null;
    const desVal = this.hasEncrypt ? decryption(val) : val;
    const data = JSON.parse(desVal);
    const { value, expire } = data;
    if (isNullOrUnDef(expire) || expire >= dayjs().valueOf()) {
      if (isObject(value) && !isNullOrUnDef(objectKey)) return value[`${objectKey}`];
      else return value;
    }
    else{
      this.remove();
      return null;
    }
  }
  set(value: any, objectKey?: string | number){
    let cacheValue;
    if (!isNullOrUnDef(objectKey)) {
      const tempCache:Record<string,any> = isObject(this.get())?this.get() as Record<string,any>:{};
      tempCache[`${objectKey}`] = value;
      cacheValue = tempCache;
    } else {
      cacheValue = value;
    }
    const cacheValueObject = {
      value: cacheValue,
      expire: !isNullOrUnDef(this.expire) ? dayjs().valueOf() + this.expire * 1000 : null,
    }
    const data = JSON.stringify(cacheValueObject);
    const encData = this.hasEncrypt ? encryption(data) : data;
    uni.setStorageSync(this.KEY, encData);
  }
  remove(){
    uni.removeStorageSync(this.KEY)
  }
}



export function createCacheStorage({ hasEncrypt = true, key, expire = null }): WebStorage {
  if (!key) throw new Error("please enter the Key");
  if(ssMap.get(key)) return ssMap.get(key)
  else {
    const newLs = new WebStorage({
      hasEncrypt,
      key,
      expire,
    });
    ssMap.set(key, newLs);
    return newLs;
  }
}

function clearStorageByType(exceptList: Array<CacheKeysEnum>){
  if(isArray(exceptList) && exceptList.length){
    ssMap.forEach((WebStorage,key)=>{
      if(!exceptList.includes(key)){
        WebStorage.remove();
      }
    })
  }
  else{
    uni.clearStorageSync()
    ssMap.clear();
  }
}

export function clearAllStorage(exceptList: Array<CacheKeysEnum>=[]) {
  clearStorageByType(exceptList);
}



