"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prescription = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Prescription = {
    [routeNameEnum_1.RouteName.Prescription]: {
        "path": "subPackages/Prescription/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.PrescriptionPlaceOrder]: {
        "path": "subPackages/Prescription/placeOrder",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.PrescriptionForm]: {
        "path": "subPackages/Prescription/prescription",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.PrescriptionList]: {
        "path": "subPackages/User/prescription/index",
        "style": {
            "navigationBarTitleText": "我的处方",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.PrescriptionDetail]: {
        "path": "subPackages/User/prescription/prescrptionDetail",
        "style": {
            "navigationBarTitleText": "处方详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.ListOfPrescriptions]: {
        "path": "subPackages/Prescription/ListOfPrescriptions/index",
        "style": {
            "navigationBarTitleText": "处方单列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.PrescriptionFormDetail]: {
        "path": "subPackages/Prescription/ListOfPrescriptions/components/prescriptionsDetail",
        "style": {
            "navigationBarTitleText": "处方单详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
};
