import { RouteName } from "@/routes/enums/routeNameEnum";
import type {  RoutesMap } from "@/routes/types";

export const Video:RoutesMap = {
    [RouteName.Video]:{
        path: "pages/Video/index",
        style: {
            "navigationBarTitleText": '',
            "enablePullDownRefresh": false,
            "navigationStyle": 'custom',
        }
    },
    [RouteName.CollectionVideoDetail]:{
        "path": "pages/Video/collection-details/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.VideoMyPage]:{
        "path": "pages/Video/videoMyPage/index",
        "style": {
           "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
        
    },
}