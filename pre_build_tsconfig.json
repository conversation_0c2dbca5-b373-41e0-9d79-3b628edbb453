{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"module": "CommonJS", "preserveValueImports": false, "sourceMap": false, "baseUrl": ".", "outDir": "./scripts/preBuild", "rootDir": "./", "paths": {"@/*": ["./src/*"], "@vant/weapp/*": ["/wxcomponents/vant/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "miniprogram-api-typings"], "strict": false}, "include": ["src/config/**/*.ts", "vite-env.d.ts"]}