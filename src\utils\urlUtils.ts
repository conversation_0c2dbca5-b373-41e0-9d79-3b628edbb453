import { isProdEnv,isTestEnv } from "@/utils/envUtils";

/**
 * 解析 url 中的参数
 * @param url
 * @returns {Object}
 */
function parseUrlParams(url) {
  const params = {};
  if (!url || url === "" || typeof url !== "string") {
    return params;
  }
  const paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return params;
  }
  const paramsArr = paramsStr.replace(/&|=/g, " ").split(" ");
  for (let i = 0; i < paramsArr.length / 2; i++) {
    const value = paramsArr[i * 2 + 1];
    params[paramsArr[i * 2]] = value === "true" ? true : value === "false" ? false : value;
  }
  return params;
}
function getURLPrefix(url?: string) {
  if (url) {
    return "";
  }
  const isProd = isProdEnv();
  // const PROFIX = isProd ? `${location.origin}` : import.meta.env.VITE_DEV_PREFIX;
  const PROFIX = isProd ? `${import.meta.env.VITE_PROD_STORE_BASE_URL}` : import.meta.env.VITE_DEV_STORE_BASE_URL;
  return `${PROFIX}`;
}

/**获取oss前缀 */
function getOssUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const hostnameSplit = location.hostname.split(".");
    const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
    hostnameSplit[0] = `${firstPart}-oss`;
    const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
    return prefix
  }
  else{
    // return import.meta.env.VITE_DEV_PREFIX
    return `sg-test-oss.jiuwei.cloud`
  }
}

/**获取store-api前缀 */
function getApiUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const hostnameSplit = location.hostname.split(".");
    const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
    hostnameSplit[0] = `${firstPart}-api`;
    const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
    return prefix
  }
  else{
    return import.meta.env.VITE_DEV_PREFIX
  }
}

/**获取图片前缀 */
function getImgUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const hostnameSplit = location.hostname.split(".");
    const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
    hostnameSplit[0] = `${firstPart}-api`;
    const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
    return prefix
  }
  else{
    return import.meta.env.VITE_DEV_BASE_URL
  }
}

/**获取basic-api前缀 */
function getBasicPlatformPrefix(){
  const isProd = isProdEnv();
  const PROFIX = isProd ? `${import.meta.env.VITE_PROD_BASIC_BASE_URL}` : import.meta.env.VITE_DEV_BASIC_BASE_URL;
  return PROFIX
}

/**获取basic-api完整地址 */
function basicPlatformUrl(url:string){
    const PROFIX = getBasicPlatformPrefix()
    return `${PROFIX}${url}`;
}

/**获取pc商城前缀 */
function getStorePCPrefix(){
  const urlSplit = `${import.meta.env.VITE_PROD_STORE_BASE_URL}`.split('.')
  urlSplit[0] = urlSplit[0].replace('-api','')
  return urlSplit.join('.')
}

/**
 * 解析查询字符串为对象（保留值为字符串格式）
 * @param queryString - 类似 "a=1&b=1874719936736362497" 的字符串
 * @returns 转换后的对象，值始终为字符串
 */
function parseQueryString(queryString: string): Record<string, string> {
  const result: Record<string, string> = {};

  queryString.split("&").forEach((pair) => {
    const [key, value] = pair.split("=");
    if (key) {
      result[key] = value ?? ""; // 如果值为空则赋为空字符串
    }
  });

  return result;
}


export { 
  parseUrlParams, 
  getURLPrefix,
  getOssUrlPrefix,
  getApiUrlPrefix,
  getImgUrlPrefix,
  basicPlatformUrl,
  getBasicPlatformPrefix,
  getStorePCPrefix,
  parseQueryString
};

