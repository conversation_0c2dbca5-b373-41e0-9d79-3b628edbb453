export interface globalStyle{
    navigationBarBackgroundColor?: string,
    navigationBarTextStyle?: string,
    navigationBarTitleText: string,
    navigationStyle?: 'default' | 'custom',
    backgroundColor?: string,
    backgroundTextStyle?: 'dark' | 'light',
    enablePullDownRefresh?: boolean,
    onReachBottomDistance?: number,
    pageOrientation?: 'auto ' | 'portrait ' | 'landscape ',
    mpWeixin?: {},
    usingComponents?: {},
    renderingMode?:string,
}
export interface Page{
    path:string,
    style?:{},
    needLogin?:boolean
}
export interface easycom{
    autoscan:boolean;
    custom?:{},
}
export interface tabBar{
    custom?: boolean,
    color?:string,
    selectedColor?:string,
    backgroundColor?:string,
    borderStyle?:string,
    blurEffect?:string,
    list:tabBarList[],
    position?:string,
    fontSize?:string,
    iconWidth?:string,
    spacing?:string,
    height?:string,
    midButton?:tabBarMidButton,
    iconfontSrc?:string,
    backgroundImage?:string,
    backgroundRepeat?:string,
    redDotColor?:string,
}
export interface tabBarList{
    pagePath:string,
    text:string,
    iconPath?:string,
    selectedIconPath?:string,
    visible?:boolean,
    iconfont?:tabBarIconfont,
}
export interface tabBarMidButton{
    width:string,
    height:string,
    text?:string,
    iconPath?:string,
    iconWidth?:string,
    backgroundImage?:string,
    iconfont?:tabBarIconfont,
}
export interface tabBarIconfont{
    text:string,
    selectedText:string,
    fontSize?:string,
    color?:string,
    selectedColor?:string,
}

export interface PagesConfig{
    pages:Page[],
    globalStyle?:globalStyle,
    easycom?:easycom
    tabBar?:tabBar,
    subPackages?:subPackage[],
}
export interface subPackage{
    root:string,
    pages:Page[],
    independent?:boolean
}