"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Distribute = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Distribute = {
    [routeNameEnum_1.RouteName.Distribute]: {
        "path": "subPackages/Distribute/index",
        "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.DistributeCustomer]: {
        "path": "subPackages/Distribute/Customer/index",
        "style": {
            "navigationBarTitleText": "客户",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.DistributeOrder]: {
        "path": "subPackages/Distribute/Order/index",
        "style": {
            "navigationBarTitleText": "订单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.DistributeOrderDetail]: {
        "path": "subPackages/Distribute/OrderDetail/index",
        "style": {
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#D3E6FF"
        }
    },
    [routeNameEnum_1.RouteName.DistributeInvite]: {
        "path": "subPackages/Distribute/Invite/index",
        "style": {
            "navigationBarTitleText": "邀请",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    }
};
