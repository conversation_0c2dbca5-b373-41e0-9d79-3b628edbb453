import { JRequest } from "@/services/index";
const enum HomeApiEnum {
  swiperList = "/applet/carouselImg/listImg",
  headerInfo = "/applet/homeLogo/getLogo",
}
interface SwiperListParams {
  CurrentPosition: number;
}
export async function swiperList(params: SwiperListParams) {
  return JRequest.get({
    url: `${HomeApiEnum.swiperList}?CurrentPosition=${params.CurrentPosition}`,
    requestConfig: {
      withToken: true,
    },
  });
}

export async function homeHeaderInfo() {
  return JRequest.get({
    url: HomeApiEnum.headerInfo,
    requestConfig: {
      withToken: true,
    },
  });
}
