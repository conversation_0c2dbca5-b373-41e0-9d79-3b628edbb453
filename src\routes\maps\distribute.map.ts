import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Distribute:RoutesMap = {
    [RouteName.Distribute]:{
        "path": "subPackages/Distribute/index",
        "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.DistributeCustomer]:{
        "path": "subPackages/Distribute/Customer/index",
        "style": {
            "navigationBarTitleText": "客户",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.DistributeOrder]:{
        "path": "subPackages/Distribute/Order/index",
        "style": {
            "navigationBarTitleText": "订单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.DistributeOrderDetail]:{
        "path": "subPackages/Distribute/OrderDetail/index",
        "style": {
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#D3E6FF"
        }
    },
    [RouteName.DistributeInvite]:{
        "path": "subPackages/Distribute/Invite/index",
        "style": {
            "navigationBarTitleText": "邀请",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    }
}