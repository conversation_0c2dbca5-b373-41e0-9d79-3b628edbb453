const fs = require("fs");
const path = require("path");
const sharp = require("sharp");

exports.cleanupByDirectory = (directory) => {
  return new Promise((resolve, reject) => {
    if (fs.existsSync(directory)) {
      try {
        fs.rmdirSync(directory, { recursive: true });
        resolve(true);
      } catch (err) {
        reject(`read directory error: ${err}`);
      }
    } else {
      resolve(true);
    }
  });
};
exports.coverFileContentByPath = (filePath, content) => {
  return new Promise((resolve, reject) => {
    try {
      const folderPath = path.dirname(filePath);
      // 确保目录存在
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }
      fs.writeFileSync(filePath, content, "utf8");
      resolve(true);
    } catch (e) {
      reject(`coverFileContentByPathError: ${e}`);
    }
  });
};

/**
 * @description 压缩图片以减小文件大小（支持PNG、JPG、JPEG）
 * @param {string} sourcePath - 源图片路径
 * @param {string} targetPath - 目标图片路径
 * @returns {Promise<void>}
 */
const compressImage = (sourcePath, targetPath) => {
  return new Promise((resolve, reject) => {
    const ext = path.extname(sourcePath).toLowerCase();

    let compressionOptions;

    // 根据文件扩展名设置不同的压缩选项
    if (ext === ".jpg" || ext === ".jpeg") {
      compressionOptions = sharp(sourcePath).jpeg({quality: 60,  progressive: true, mozjpeg: true });
    } else if (ext === ".png") {
      compressionOptions = sharp(sourcePath).png({ compressionLevel: 9, quality: 70, progressive: true ,  palette: true   });
    } else {
      return reject(`不支持的文件格式: ${ext}`);
    }

    // 执行压缩并保存文件
    compressionOptions.toFile(targetPath, (err, info) => {
      if (err) {
        reject(`无法压缩文件: ${err}`);
      } else {
        resolve(info);
      }
    });
  });
};

/**
 * @description 复制图片到目标路径
 * @param {string} sourcePath - 源图片路径
 * @param {string} targetPath - 目标图片路径
 * @returns {Promise<void>}
 */
const copyImage = (sourcePath, targetPath) => {
  return new Promise((resolve, reject) => {
    fs.copyFile(sourcePath, targetPath, (err) => {
      if (err) {
        reject(`无法复制文件: ${err}`);
      } else {
        resolve();
      }
    });
  });
};

/**
 * @description 筛选图片工具函数
 * @param {string} sourceDir - 源文件夹路径
 * @param {string} targetDir - 目标文件夹路径
 * @param {string} outputFilePath - 输出文件路径
 * @param {number} sizeLimitKB - 文件大小限制（以KB为单位）
 * @param {number} compressSizeLimitKB - 文件压缩大小限制（以KB为单位）
 * @returns {Promise<void>}
 */
exports.filterLargeImages = (
  sourceDir,
  targetDir,
  outputFilePath,
  sizeLimitKB,
  compressSizeLimitKB
) => {
  return new Promise((resolve, reject) => {
    try {
      let largeImageSrcList = [];
      let pendingOperations = 0;

      // 创建目标文件夹
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // 创建输出文件夹
      const outputFolderPath = path.dirname(outputFilePath);
      if (!fs.existsSync(outputFolderPath)) {
        fs.mkdirSync(outputFolderPath, { recursive: true });
      }

      // 获取目录下所有文件并筛选大图
      const processFiles = (dir) => {
        pendingOperations++;
        fs.readdir(dir, (err, files) => {
          if (err) {
            reject(`无法读取目录: ${err}`);
            return;
          }

          files.forEach((file) => {
            const filePath = path.join(dir, file);

            pendingOperations++;
            fs.stat(filePath, (err, stats) => {
              if (err) {
                console.error(`无法获取文件信息: ${err}`);
                return;
              }

              if (stats.isDirectory()) {
                processFiles(filePath);
              } else {
                const imageExtensions = [".png", ".jpg", ".jpeg"];
                const fileExt = path.extname(file).toLowerCase();

                if (
                  imageExtensions.includes(fileExt) &&
                  stats.size > sizeLimitKB * 1024
                ) {
                  const relativePath = path.relative(sourceDir, filePath);
                  const targetPath = path.join(targetDir, relativePath);

                  // 创建目标文件夹路径
                  const targetDirPath = path.dirname(targetPath);
                  if (!fs.existsSync(targetDirPath)) {
                    fs.mkdirSync(targetDirPath, { recursive: true });
                  }

                  // 压缩并复制文件到目标文件夹
                  pendingOperations++;
                  // 判断是否需要压缩
                  if (stats.size > compressSizeLimitKB * 1024) {
                    console.log("Compressing image: " , filePath);
                    compressImage(filePath, targetPath).then(() => {
                      // 规范化路径，存储相对于 sourceDir 的路径
                      const normalizedPath = `/${filePath.replace(/\\/g, "/")}`;
  
                      // 保存相对路径到数组
                      largeImageSrcList.push(normalizedPath.replace("/src", ""));
  
                      pendingOperations--;
                      checkCompletion();
                    }).catch((err) => {
                      console.log(`压缩文件失败: ${err}`);
                      pendingOperations--;
                      checkCompletion();
                    });
                  } else {
                    console.log("CopyImage image: " , filePath);
                    copyImage(filePath, targetPath)
                      .then(() => {
                        // 规范化路径，存储相对于 sourceDir 的路径
                        const normalizedPath = `/${filePath.replace(/\\/g, "/")}`;
    
                        // 保存相对路径到数组
                        largeImageSrcList.push(normalizedPath.replace("/src", ""));

                        pendingOperations--;
                        checkCompletion();
                      })
                      .catch((err) => {
                        console.error(`复制文件失败: ${err}`);
                        pendingOperations--;
                        checkCompletion();
                      });
                  }
                }
              }
              pendingOperations--;
              checkCompletion();
            });
          });
          pendingOperations--;
          checkCompletion();
        });
      };

      const checkCompletion = () => {
        if (pendingOperations === 0) {
          // 清空 outputFilePath 文件内容
          fs.writeFileSync(outputFilePath, '', 'utf8');
          
          // 写入新的内容
          fs.writeFileSync(
            outputFilePath,
            `exports.largeImageSrcList = ${JSON.stringify(largeImageSrcList, null, 2)};\n`,
            'utf8'
          );
          
          // 处理完成，resolve Promise
          resolve(true);
        }
      };

      processFiles(sourceDir);

    } catch (err) {
      console.log(`操作失败: ${err}`);
      reject(`操作失败: ${err}`);
    }
  });
};



