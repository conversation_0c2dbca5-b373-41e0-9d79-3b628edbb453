"use strict";
// const MapsModules = import.meta.glob('./*.map.ts',{ eager: true })
// const MapsObject = {}
// Object.values(MapsModules).forEach((modules)=>{
//   for(let key in modules as object){
//     Object.assign(MapsObject,modules[key])
//   }
// })
Object.defineProperty(exports, "__esModule", { value: true });
exports.routesMap = void 0;
// export const routesMap = MapsObject
const account_map_1 = require("./account.map");
const cart_map_1 = require("./cart.map");
const cate_map_1 = require("./cate.map");
const independent_map_1 = require("./independent.map");
const order_map_1 = require("./order.map");
const prescription_map_1 = require("./prescription.map");
const integralHome_map_1 = require("./integralHome.map");
const video_map_1 = require("./video.map");
const distribute_map_1 = require("./distribute.map");
const s_map_1 = require("./s.map");
const stream_map_1 = require("./stream.map");
const message_map_1 = require("./message.map");
const inquiry_map_1 = require("./inquiry.map");
const MapsObject = {
    ...account_map_1.User,
    ...cart_map_1.Cart,
    ...cate_map_1.Cate,
    ...independent_map_1.Independent,
    ...order_map_1.Order,
    ...prescription_map_1.Prescription,
    ...integralHome_map_1.IntegralHome,
    ...video_map_1.Video,
    ...distribute_map_1.Distribute,
    ...s_map_1.S,
    ...message_map_1.Message,
    ...inquiry_map_1.Inquiry,
    ...stream_map_1.Stream,
};
exports.routesMap = MapsObject;
