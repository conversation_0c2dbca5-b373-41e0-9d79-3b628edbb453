"use strict";
// const MapsModules = import.meta.glob('./*.map.ts',{ eager: true })
// const MapsObject = {}
// Object.values(MapsModules).forEach((modules)=>{
//   for(let key in modules as object){
//     Object.assign(MapsObject,modules[key])
//   }
// })
Object.defineProperty(exports, "__esModule", { value: true });
exports.routesMap = void 0;
// export const routesMap = MapsObject
const Home_1 = require("./Home");
const Cate_1 = require("./Cate");
const MapsObject = {
    ...Home_1.Home,
    ...Cate_1.Cate,
};
exports.routesMap = MapsObject;
