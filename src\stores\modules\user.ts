import { defineStore } from "pinia";
import { stores } from "@/stores";
import { StoreNamesEnum } from "@/enum/storeNamesEnum";
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/cache/config";
import {UserType, IsDistributor, isBindPhysType} from "@/enum/userTypeEnum";


export interface UserInfoInterface{
  img:string,
  nickname:string,
  mobile: string,
  idNo:string,
  name: string,
  gender: string,
  isAuth: boolean,
  id:string,
  /**当前登录用户的type，默认为会员 */
  type:UserType,
  /**群管id */
  mgrId?:string,
  /**是否是分销员 */
  isDistributor:IsDistributor,
  encryptUnionId?:string,
  isBindPhys:isBindPhysType
}

interface UserInfoState{
  userInfo:UserInfoInterface,
  _token:string | null,
  isBindGroup:boolean,
  defaultChannelId:string,
  _openId: string | null,
  _unionId:string
}

export const userInfoStore = defineStore(StoreNamesEnum.userInfo, {
  state: ():UserInfoState => {
    return {
      userInfo: {
        img: '',
        nickname: '',
        mobile: '',
        idNo: '',
        name: '',
        gender: '',
        id: '',
        isAuth: false,
        type:UserType.Member,
        isDistributor:IsDistributor.No,
        encryptUnionId:'',
        isBindPhys:isBindPhysType.UNBOUND
      },
      _unionId:null,
      _token: null,
      isBindGroup:true,
      /**默认公众号EntityId */
      defaultChannelId:'',
      /** openId(后端加密后) */
      _openId: null
    };
  },
  getters: {
    unionId: state => {
      if (!state._unionId) {
        try {
          const authStorage = createCacheStorage(CacheConfig.UnionId);
          const _tokenCache = authStorage.get();
          state._unionId = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._unionId;
    },
    token: state => {
      if (!state._token) {
        try {
          const authStorage = createCacheStorage(CacheConfig.Token);
          const _tokenCache = authStorage.get();
          state._token = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._token;
    },
    openId: state => {
      if (!state._openId) {
        try {
          const authStorage = createCacheStorage(CacheConfig.OpenId);
          const _openIdCache = authStorage.get();
          state._openId = _openIdCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._openId;
    },
    info: (state):UserInfoInterface => {
      if (!state.userInfo.id) {
        try {
          const userInfoStorage = createCacheStorage(CacheConfig.UserInfo);
          const _userInfoCache = userInfoStorage.get();
          if(_userInfoCache){
            state.userInfo = _userInfoCache;
          }
        } catch (e) {
          console.error(e);
        }
      }
      return state.userInfo;
    }
  },
  actions: {
    setToken(token: string) {
      this._token = token;
      const authStorage = createCacheStorage(CacheConfig.Token);
      authStorage.set(token);
    },
    setUnionId(unionId: string) {
      this._unionId = unionId;
      const authStorage = createCacheStorage(CacheConfig.UnionId);
      authStorage.set(unionId);
    },
    setOpenId(openId: string) {
      this._openId = openId;
      const authStorage = createCacheStorage(CacheConfig.OpenId);
      authStorage.set(openId);
    },
    setUserInfo(info: any) {
      const data = {
        ...info,
        isAuth: !!(info.name && info.idNo),
      }
      this.userInfo = data;
      const userInfoStorage = createCacheStorage(CacheConfig.UserInfo);
      userInfoStorage.set(data);
    },
    setBindGroup(isBind: boolean) {
      this.isBindGroup = isBind;
    },
    setDefaultChannelId(channelId:string){
      this.defaultChannelId = channelId
    }
  },
});

export function useUserInfoStoreWithoutSetup() {
  return userInfoStore(stores);
}
