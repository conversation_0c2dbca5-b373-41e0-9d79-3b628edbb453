<template>
  <view v-show="!isHiddenTabbar" :class="['tab-bar', { hidden: isHiddenTabbar }]" :style="{ backgroundColor: isCheckedVideo ? 'black':'white' }" >
    <view v-for="item in navigationListRef" :key="item.pagePath" class="tab-bar-item" @click="() => switchTab(item)">
      <image :src="selectedTabKeyRef === item.pagePath ? item.selectedIconPath : item.iconPath"></image>
      <view :style="`color: ${selectedTabKeyRef === item.pagePath ? selectedColor : color}`">{{ item.text }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useTabbar } from './hooks/useTabbar';
const { loadNavigationConfig, selectedTabKeyRef, navigationListRef, isHiddenTabbar,setTabbarDisplay,isCheckedVideo } = useTabbar()
const selectedColor = '#333'
const color = '#999'

function switchTab(item) {
  const { pagePath, pointPage } = item
    wx.switchTab({ url: `/${pagePath}` })
  
}

onMounted(() => {
  loadNavigationConfig()
})

</script>

<style scoped lang="less">
@import '@/components/Tabbar/style.scss';

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: $tabber-height;
  display: flex;
  flex-direction: row;
  pointer-events: auto;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10;
 /* 添加平滑的背景颜色过渡效果 */
 transition: background-color 0.5s ease;
  // border-radius: 14px 14px 0px 0px;
  // transition: height linear 0.2s;
  &.hidden {
    height: 0px;
    display: none;
  }
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tab-bar-item image {
  width: 48rpx;
  height: 48rpx;
}

.tab-bar-item view {
  font-size: 20rpx;
}
</style>