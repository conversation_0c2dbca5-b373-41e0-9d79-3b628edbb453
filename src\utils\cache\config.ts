
import type { CacheParams } from "./type";
import { CacheKeysEnum } from "@/enum/cacheKeysEnum";

export const CacheConfig: Record<string, CacheParams> = {
  Token: {
    key: CacheKeysEnum.token,
  },
  OpenId: {
    key: CacheKeysEnum.openId,
  },
  UserInfo: {
    key: CacheKeysEnum.userInfo,
    expire: 60 * 60 * 24 * 7,
  },
  Video: {
    key: CacheKeysEnum.video,
  },
  sharingInfoKey: {
    key: CacheKeysEnum.sharingInfoKey,
  },
  UnionId: {
    key: CacheKeysEnum.unionId,
  },
};
