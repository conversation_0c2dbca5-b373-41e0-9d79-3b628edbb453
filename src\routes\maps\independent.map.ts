import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Independent:RoutesMap = {
    [RouteName.Login]:{
        "path": "pages/Login/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.jtLogin]:{
        "path": "pages/Login/components/jtLogin",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.Search]:{
        "path": "pages/Search/index",
        "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false
        }
    },	
    [RouteName.Home]:{
        "path": "pages/Home/index",
        "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
        }
    },
    [RouteName.Webview]:{
        "path": "pages/Webview/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
        }
    },
    [RouteName.Inquiry]:{
        "path": "pages/Inquiry/index",
        "style": {
            "navigationBarTitleText": "问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    }
}