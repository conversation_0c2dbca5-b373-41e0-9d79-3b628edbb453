"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Independent = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Independent = {
    [routeNameEnum_1.RouteName.Login]: {
        "path": "pages/Login/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.jtLogin]: {
        "path": "pages/Login/components/jtLogin",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.Search]: {
        "path": "pages/Search/index",
        "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false
        }
    },
    [routeNameEnum_1.RouteName.Home]: {
        "path": "pages/Home/index",
        "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
        }
    },
    [routeNameEnum_1.RouteName.Webview]: {
        "path": "pages/Webview/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
        }
    },
    [routeNameEnum_1.RouteName.Inquiry]: {
        "path": "pages/Inquiry/index",
        "style": {
            "navigationBarTitleText": "问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    }
};
