import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const IntegralHome:RoutesMap = {
    [RouteName.IntegralHome]:{
        "path": "pages/IntegralHome/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.PointsRecord]:{
        "path": "pages/IntegralHome/PointsRecord/index",
        "style": {
            "navigationBarTitleText": "积分明细",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.PointsExchange]:{
        "path": "pages/IntegralHome/PointsExchange/index",
        "style": {
            "navigationBarTitleText": "我能兑",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
}