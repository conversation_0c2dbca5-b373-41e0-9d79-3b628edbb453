"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegralHome = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.IntegralHome = {
    [routeNameEnum_1.RouteName.IntegralHome]: {
        "path": "pages/IntegralHome/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.PointsRecord]: {
        "path": "pages/IntegralHome/PointsRecord/index",
        "style": {
            "navigationBarTitleText": "积分明细",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.PointsExchange]: {
        "path": "pages/IntegralHome/PointsExchange/index",
        "style": {
            "navigationBarTitleText": "我能兑",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
};
