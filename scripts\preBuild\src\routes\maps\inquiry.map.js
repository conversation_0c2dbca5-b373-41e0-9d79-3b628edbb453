"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Inquiry = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.Inquiry = {
    [routeNameEnum_1.RouteName.InquiryDoctorList]: {
        "path": "subPackages/Inquiry/DoctorList/index",
        "style": {
            "navigationBarTitleText": "医生列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.InquiryDoctorSearch]: {
        "path": "subPackages/Inquiry/DoctorSearch/index",
        "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.InquiryDoctorDetail]: {
        "path": "subPackages/Inquiry/DoctorDetail/index",
        "style": {
            "navigationBarTitleText": "问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [routeNameEnum_1.RouteName.InquirySymptomDescription]: {
        "path": "subPackages/Inquiry/SymptomDescription/index",
        "style": {
            "navigationBarTitleText": "症状描述",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.InquiryPending]: {
        "path": "subPackages/Inquiry/DiagnosePending/index",
        "style": {
            "navigationBarTitleText": "待接诊",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.InquiryChat]: {
        "path": "subPackages/Inquiry/InquiryChat/index",
        "style": {
            "navigationBarTitleText": "问诊聊天",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [routeNameEnum_1.RouteName.Live]: {
        "path": "subPackages/Live/index",
        "style": {
            "navigationBarTitleText": "视频问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    }
};
