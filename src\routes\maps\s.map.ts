import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const S:RoutesMap = {
    [RouteName.Demo]:{
        "path": "subPackages/S/Demo/index",
        "style": {
            "navigationBarTitleText": "内容详情",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.SWebView]:{
        "path": "subPackages/S/WebView/index",
        "style": {
            "navigationBarTitleText": "详情",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.Check]:{
        "path": "subPackages/S/WebView/Check",
        "style": {
            "navigationBarTitleText": "加载中",
            "enablePullDownRefresh": false,
        }
    },
   
}