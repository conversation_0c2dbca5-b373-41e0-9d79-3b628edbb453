import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const Inquiry:RoutesMap = {
    [RouteName.InquiryDoctorList]:{
        "path": "subPackages/Inquiry/DoctorList/index",
        "style": {
            "navigationBarTitleText": "医生列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.InquiryDoctorSearch]:{
        "path": "subPackages/Inquiry/DoctorSearch/index",
        "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.InquiryDoctorDetail]:{
        "path": "subPackages/Inquiry/DoctorDetail/index",
        "style": {
            "navigationBarTitleText": "问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.InquirySymptomDescription]:{
        "path": "subPackages/Inquiry/SymptomDescription/index",
        "style": {
            "navigationBarTitleText": "症状描述",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.InquiryPending]:{
        "path": "subPackages/Inquiry/DiagnosePending/index",
        "style": {
            "navigationBarTitleText": "待接诊",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.InquiryChat]:{
        "path": "subPackages/Inquiry/InquiryChat/index",
        "style": {
            "navigationBarTitleText": "问诊聊天",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.Live]:{
        "path": "subPackages/Live/index",
        "style": {
            "navigationBarTitleText": "视频问诊",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    }   
}