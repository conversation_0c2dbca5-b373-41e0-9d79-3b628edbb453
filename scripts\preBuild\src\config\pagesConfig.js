"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prodConfig = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
const maps_1 = require("@/routes/maps");
function getSubPackagesUrl(pageConfigs) {
    return pageConfigs.map((pageConfig) => {
        if (pageConfig.path.includes("subPackages")) {
            return {
                ...pageConfig,
                path: pageConfig.path.replace(/^subPackages\/[^\/]+\//, ""),
            };
        }
        else {
            return pageConfig;
        }
    });
}
exports.prodConfig = {
    pages: [
        maps_1.routesMap[routeNameEnum_1.RouteName.Home],
        maps_1.routesMap[routeNameEnum_1.RouteName.Search],
        maps_1.routesMap[routeNameEnum_1.RouteName.Cate],
        maps_1.routesMap[routeNameEnum_1.RouteName.Login],
        maps_1.routesMap[routeNameEnum_1.RouteName.Cart],
        maps_1.routesMap[routeNameEnum_1.RouteName.User],
        maps_1.routesMap[routeNameEnum_1.RouteName.Therapy],
        maps_1.routesMap[routeNameEnum_1.RouteName.Pay],
        maps_1.routesMap[routeNameEnum_1.RouteName.Webview],
        maps_1.routesMap[routeNameEnum_1.RouteName.IntegralHome],
        maps_1.routesMap[routeNameEnum_1.RouteName.PointsRecord],
        maps_1.routesMap[routeNameEnum_1.RouteName.PointsExchange],
        maps_1.routesMap[routeNameEnum_1.RouteName.jtLogin],
        maps_1.routesMap[routeNameEnum_1.RouteName.Video],
        maps_1.routesMap[routeNameEnum_1.RouteName.CollectionVideoDetail],
        maps_1.routesMap[routeNameEnum_1.RouteName.VideoMyPage],
        maps_1.routesMap[routeNameEnum_1.RouteName.Stream],
        maps_1.routesMap[routeNameEnum_1.RouteName.Message],
        maps_1.routesMap[routeNameEnum_1.RouteName.Inquiry],
    ],
    subPackages: [
        {
            root: "subPackages/User",
            /** 我的页面 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.UserAddress],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserAddressEdit],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserSettings],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserVerify],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserVerifyResult],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserBuyerList],
                maps_1.routesMap[routeNameEnum_1.RouteName.UserBuyerEdit],
                maps_1.routesMap[routeNameEnum_1.RouteName.PrescriptionList],
                maps_1.routesMap[routeNameEnum_1.RouteName.PrescriptionDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.ListOfMedicalConsultations],
                maps_1.routesMap[routeNameEnum_1.RouteName.MedicalConsultationFormDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.MyDoctorList],
                maps_1.routesMap[routeNameEnum_1.RouteName.MyAppointment],
                maps_1.routesMap[routeNameEnum_1.RouteName.AppointmentForMedicalConsultation],
                maps_1.routesMap[routeNameEnum_1.RouteName.Balance],
            ]),
        },
        {
            root: "subPackages/Order",
            /** 订单相关 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.Order],
                maps_1.routesMap[routeNameEnum_1.RouteName.OrderDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.CancelDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.LogisticsDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.RefundDetails],
                maps_1.routesMap[routeNameEnum_1.RouteName.AfterSaleDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.OrderConfirm],
            ]),
        },
        {
            root: "subPackages/PlaceOrder",
            /** 代下单 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.OrderAgent],
                maps_1.routesMap[routeNameEnum_1.RouteName.OrderSupplementary],
                maps_1.routesMap[routeNameEnum_1.RouteName.OrderAgentResult],
            ]),
        },
        {
            root: "subPackages/Distribute",
            /** 分销页面 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.Distribute],
                maps_1.routesMap[routeNameEnum_1.RouteName.DistributeCustomer],
                maps_1.routesMap[routeNameEnum_1.RouteName.DistributeOrder],
                maps_1.routesMap[routeNameEnum_1.RouteName.DistributeOrderDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.DistributeInvite],
            ]),
        },
        {
            root: "subPackages/Prescription",
            /** 处方相关 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.Prescription],
                maps_1.routesMap[routeNameEnum_1.RouteName.PrescriptionPlaceOrder],
                maps_1.routesMap[routeNameEnum_1.RouteName.PrescriptionForm],
                maps_1.routesMap[routeNameEnum_1.RouteName.ListOfPrescriptions],
                maps_1.routesMap[routeNameEnum_1.RouteName.PrescriptionFormDetail],
            ]),
        },
        {
            root: "subPackages/GoodsDetail",
            /** 商品详情 */
            pages: getSubPackagesUrl([maps_1.routesMap[routeNameEnum_1.RouteName.GoodsDetail]]),
        },
        {
            root: "subPackages/S",
            /** 社群业务相关 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.Demo],
                maps_1.routesMap[routeNameEnum_1.RouteName.SWebView],
                maps_1.routesMap[routeNameEnum_1.RouteName.Check],
                // routesMap[RouteName.News],
            ]),
        },
        {
            root: "subPackages/Inquiry",
            /** 问诊相关 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.InquiryDoctorList],
                maps_1.routesMap[routeNameEnum_1.RouteName.InquiryDoctorSearch],
                maps_1.routesMap[routeNameEnum_1.RouteName.InquiryDoctorDetail],
                maps_1.routesMap[routeNameEnum_1.RouteName.InquirySymptomDescription],
                maps_1.routesMap[routeNameEnum_1.RouteName.InquiryPending],
                maps_1.routesMap[routeNameEnum_1.RouteName.InquiryChat],
            ]),
        },
        {
            root: "subPackages/Live",
            /** 直播相关 */
            pages: getSubPackagesUrl([
                maps_1.routesMap[routeNameEnum_1.RouteName.Live],
            ]),
        }
    ],
    // "easycom": {
    // 	"autoscan": true,
    // 	"custom": {
    // 		/** 匹配分包goodsDetail页面中的组件 */
    // 		"^goods-title": "@/pages/Home/components/GoodsTitle.vue"
    // 	}
    // },
    globalStyle: {
        navigationBarTextStyle: "black",
        navigationBarTitleText: "商城",
        navigationBarBackgroundColor: "#F8F8F8",
        backgroundColor: "#F8F8F8",
        usingComponents: {
            "mp-html": "/wxcomponents/mp-html/index",
            "van-icon": "/wxcomponents/vant/icon/index",
            "van-row": "/wxcomponents/vant/row/index",
            "van-col": "/wxcomponents/vant/col/index",
            "van-button": "/wxcomponents/vant/button/index",
            "van-field": "/wxcomponents/vant/field/index",
            "van-search": "/wxcomponents/vant/search/index",
            "van-tab": "/wxcomponents/vant/tab/index",
            "van-tabs": "/wxcomponents/vant/tabs/index",
            "van-cell": "/wxcomponents/vant/cell/index",
            "van-cell-group": "/wxcomponents/vant/cell-group/index",
            "van-divider": "/wxcomponents/vant/divider/index",
            "van-tag": "/wxcomponents/vant/tag/index",
            "van-loading": "/wxcomponents/vant/loading/index",
            "van-empty": "/wxcomponents/vant/empty/index",
            "van-notify": "/wxcomponents/vant/notify/index",
            "van-dropdown-menu": "/wxcomponents/vant/dropdown-menu/index",
            "van-dropdown-item": "/wxcomponents/vant/dropdown-item/index",
            "van-transition": "/wxcomponents/vant/transition/index",
            "van-sidebar": "/wxcomponents/vant/sidebar/index",
            "van-sidebar-item": "/wxcomponents/vant/sidebar-item/index",
            "van-switch": "/wxcomponents/vant/switch/index",
            "van-action-sheet": "/wxcomponents/vant/action-sheet/index",
            "van-popup": "/wxcomponents/vant/popup/index",
            "van-picker": "/wxcomponents/vant/picker/index",
            "van-checkbox": "/wxcomponents/vant/checkbox/index",
            "van-checkbox-group": "/wxcomponents/vant/checkbox-group/index",
            "van-slider": "/wxcomponents/vant/slider/index",
            "van-image": "/wxcomponents/vant/image/index",
            "van-overlay": "/wxcomponents/vant/overlay/index",
            "van-toast": "/wxcomponents/vant/toast/index",
            "van-card": "/wxcomponents/vant/card/index",
            "van-uploader": "/wxcomponents/vant/uploader/index",
            "van-dialog": "/wxcomponents/vant/dialog/index",
            "van-stepper": "/wxcomponents/vant/stepper/index",
            "van-cascader": "/wxcomponents/vant/cascader/index",
            "van-radio": "/wxcomponents/vant/radio/index",
            "van-radio-group": "/wxcomponents/vant/radio-group/index",
            "van-swipe-cell": "/wxcomponents/vant/swipe-cell/index",
            "van-steps": "/wxcomponents/vant/steps/index",
            "van-nav-bar": "/wxcomponents/vant/nav-bar/index",
            "van-datetime-picker": "/wxcomponents/vant/datetime-picker/index",
            "van-count-down": "/wxcomponents/vant/count-down/index",
            "van-progress": "/wxcomponents/vant/progress/index",
            "van-config-provider": "/wxcomponents/vant/config-provider/index",
            "van-skeleton": "/wxcomponents/vant/skeleton/index",
            "van-sticky": "/wxcomponents/vant/sticky/index",
        },
    },
    tabBar: {
        custom: true,
        selectedColor: "#333333",
        list: [
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.IntegralHome].path,
                text: "积分商城",
                iconPath: "static/images/tabbar/integralHome.png",
                selectedIconPath: "static/images/tabbar/integralHome-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Home].path,
                text: "首页",
                iconPath: "static/images/tabbar/cate.png",
                selectedIconPath: "static/images/tabbar/cate-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Cart].path,
                text: "购物车",
                iconPath: "static/images/tabbar/cart.png",
                selectedIconPath: "static/images/tabbar/cart-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.User].path,
                text: "我的",
                iconPath: "static/images/tabbar/home.png",
                selectedIconPath: "static/images/tabbar/home-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Therapy].path,
                text: "健康疗法",
                iconPath: "static/images/tabbar/home.png",
                selectedIconPath: "static/images/tabbar/home-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Cate].path,
                text: "健康商城",
                iconPath: "static/images/tabbar/home.png",
                selectedIconPath: "static/images/tabbar/home-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Video].path,
                text: "视频",
                iconPath: "static/images/tabbar/home.png",
                selectedIconPath: "static/images/tabbar/home-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Inquiry].path,
                text: "问诊",
                iconPath: "static/images/tabbar/inquiry.png",
                selectedIconPath: "static/images/tabbar/inquiry-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Message].path,
                text: "消息",
                iconPath: "static/images/tabbar/message.png",
                selectedIconPath: "static/images/tabbar/message-selected.png",
            },
            {
                pagePath: maps_1.routesMap[routeNameEnum_1.RouteName.Stream].path,
                text: "直播",
                iconPath: "static/images/tabbar/stream.png",
                selectedIconPath: "static/images/tabbar/stream-setected.png",
            },
        ],
    },
};
