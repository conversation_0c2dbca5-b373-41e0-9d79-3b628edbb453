"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteName = void 0;
var RouteName;
(function (RouteName) {
    /** 商品列表 */
    RouteName["Cate"] = "jw-store-mp-cate";
    /** 疗法列表 */
    RouteName["Therapy"] = "jw-store-mp-therapy";
    /** 商品详情 */
    RouteName["GoodsDetail"] = "jw-store-mp-goods-detail";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 登录 */
    RouteName["Login"] = "jw-store-mp-login";
    /** 登录 */
    RouteName["jtLogin"] = "jw-store-mp-jt-login";
    /** 首页 */
    RouteName["Home"] = "jw-store-mp-home";
    /** 搜索 */
    RouteName["Search"] = "jw-store-mp-search";
    /** webview */
    RouteName["Webview"] = "jw-store-mp-webview";
    /** 问诊 */
    RouteName["Inquiry"] = "jw-store-mp-inquiry";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 订单 */
    RouteName["Order"] = "jw-store-mp-order";
    /** 订单详情 */
    RouteName["OrderDetail"] = "jw-store-mp-order-detail";
    /** 取消订单 */
    RouteName["CancelDetail"] = "jw-store-mp-cancel-detail";
    /** 取消订单 */
    RouteName["LogisticsDetail"] = "jw-store-mp-logistics-detail";
    /** 申请退款 */
    RouteName["RefundDetails"] = "jw-store-mp-refund-detail";
    /** 售后详情 */
    RouteName["AfterSaleDetail"] = "jw-store-mp-after-sale-detail";
    /** 订单确认 */
    RouteName["OrderConfirm"] = "jw-store-mp-order-confirm";
    /** 支付页面 */
    RouteName["Pay"] = "jw-store-mp-order-pay";
    /** 代下单*/
    RouteName["OrderAgent"] = "jw-store-mp-order-agent";
    /** 代下单补充订单信息 */
    RouteName["OrderSupplementary"] = "jw-store-mp-order-supplementary";
    /** 代下单结果页 */
    RouteName["OrderAgentResult"] = "jw-store-mp-order-result";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 处方 */
    RouteName["Prescription"] = "jw-store-mp-prescription";
    /** 处方下单 */
    RouteName["PrescriptionPlaceOrder"] = "jw-store-mp-prescription-place-order";
    /** 处方表单 */
    RouteName["PrescriptionForm"] = "jw-store-mp-prescription-form";
    /** 处方列表 */
    RouteName["PrescriptionList"] = "jw-store-mp-prescription-list";
    /** 处方详情 */
    RouteName["PrescriptionDetail"] = "jw-store-mp-prescription-detail";
    /** 处方单列表 */
    RouteName["ListOfPrescriptions"] = "jw-store-mp-list-of-prescriptions";
    /** 处方单详情 */
    RouteName["PrescriptionFormDetail"] = "jw-store-mp-list-of-prescriptions-form-detail";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 购物车 */
    RouteName["Cart"] = "jw-store-mp-cart";
    /** 商城视频 */
    RouteName["Video"] = "jw-store-mp-video";
    /** 收藏页视频详情 */
    RouteName["CollectionVideoDetail"] = "jw-store-mp-collection-video-detail";
    /** 视频我的主页*/
    RouteName["VideoMyPage"] = "jw-store-mp-video-my-page";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 我的 */
    RouteName["User"] = "jw-store-mp-user";
    /** 用户设置 */
    RouteName["UserSettings"] = "jw-store-mp-user-settings";
    /** 用户认证 */
    RouteName["UserVerify"] = "jw-store-mp-user-verify";
    /** 用户认证结果 */
    RouteName["UserVerifyResult"] = "jw-store-mp-user-verify-result";
    /** 用户地址 */
    RouteName["UserAddress"] = "jw-store-mp-user-address";
    /** 用户地址编辑 */
    RouteName["UserAddressEdit"] = "jw-store-mp-user-address-edit";
    /** 用药人列表 */
    RouteName["UserBuyerList"] = "jw-store-mp-user-buyer-list";
    /** 用药人信息编辑 */
    RouteName["UserBuyerEdit"] = "jw-store-mp-user-buyer-edit";
    /** 问诊列表 */
    RouteName["ListOfMedicalConsultations"] = "jw-store-mp-list-of-medical-consultations";
    /** 问诊详情 */
    RouteName["MedicalConsultationFormDetail"] = "jw-store-mp-list-of-medical-consultations-form-detail";
    /** 消息列表 */
    RouteName["Message"] = "jw-store-mp-message";
    /** 我的医生 */
    RouteName["MyDoctorList"] = "jw-store-mp-my-doctor-list";
    /** 我的预约 */
    RouteName["MyAppointment"] = "jw-store-mp-my-appointment";
    /** 医助后台 */
    RouteName["AppointmentForMedicalConsultation"] = "jw-store-mp-appointment-for-medical-consultation";
    /** 账户余额 */
    RouteName["Balance"] = "jw-store-mp-balance";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    RouteName["IntegralHome"] = "jw-store-mp-integral-home";
    RouteName["PointsRecord"] = "jw-store-mp-points-record";
    /**我能兑 */
    RouteName["PointsExchange"] = "jw-store-mp-points-exchange";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 分销中心 */
    RouteName["Distribute"] = "jw-store-mp-distribute";
    /** 客户列表 */
    RouteName["DistributeCustomer"] = "jw-store-mp-distribute-customer";
    /** 分销订单 */
    RouteName["DistributeOrder"] = "jw-store-mp-distribute-order";
    /** 分销订单详情 */
    RouteName["DistributeOrderDetail"] = "jw-store-mp-distribute-order-detail";
    /** 分销邀请客户 */
    RouteName["DistributeInvite"] = "jw-store-mp-distribute-invite";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 首页 */
    RouteName["SHome"] = "jw-group-mp-home";
    /** Exam */
    RouteName["Demo"] = "jw-group-mp-demo";
    RouteName["SWebView"] = "jw-group-mp-webview";
    RouteName["Check"] = "jw-group-mp-check";
    /** 资讯 */
    RouteName["News"] = "jw-group-mp-news";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 医生列表 */
    RouteName["InquiryDoctorList"] = "jw-group-mp-doctor-list";
    /** 医生搜索 */
    RouteName["InquiryDoctorSearch"] = "jw-group-mp-doctor-search";
    /** 医生首页 */
    RouteName["InquiryDoctorDetail"] = "jw-group-mp-doctor-detail";
    /** 症状描述 */
    RouteName["InquirySymptomDescription"] = "jw-group-mp-symptom-description";
    /** 待接诊 */
    RouteName["InquiryPending"] = "jw-group-mp-pending";
    /** 问诊聊天 */
    RouteName["InquiryChat"] = "jw-group-mp-inquiry-chat";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 直播 */
    RouteName["Stream"] = "jw-store-mp-stream";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
(function (RouteName) {
    /** 视频问诊页面 */
    RouteName["Live"] = "jw-store-mp-live";
})(RouteName = exports.RouteName || (exports.RouteName = {}));
