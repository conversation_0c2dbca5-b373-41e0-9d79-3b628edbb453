import { isFunction, isNumber, isObject } from "@/utils/isUtils";
import type { NavigateBackParams, NavigateDefaultParams, NavigateToParams } from "../types";
import { getPageUrlByRouteName } from "./routeUtils";


function urlChangedByProps({url,props}:NavigateDefaultParams){
    let _url = getPageUrlByRouteName(url)
    if(isObject(props) && Object.keys(props).length){
        Object.entries(props).forEach((item,index)=>{
            _url = `${_url}${index == 0?'?':'&'}${item.join('=')}`
        })
    }
    return _url
}

export function navigateTo(params:NavigateToParams){    
    return uni.navigateTo({
        ...params,
        url:urlChangedByProps(params)
    })
}
export function redirectTo(params:NavigateDefaultParams){
    return uni.redirectTo({
        ...params,
        url:urlChangedByProps(params)
    })
}
export function reLaunch(params:NavigateDefaultParams){
    return uni.reLaunch({
        ...params,
        url:urlChangedByProps(params)
    })
}
export function switchTab(params:Omit<NavigateDefaultParams,'props'>){
    return uni.switchTab({
        ...params,
        url:urlChangedByProps(params)
    })
}
export function navigateBack(params:NavigateBackParams = {}){
    const index = getCurrentPages().length
    if((index == 1) && !isFunction(params.fail)){
        return Promise.reject('navigateBack fail')
    }
    else{
        return uni.navigateBack({
            ...params,
        })
    }
   
}