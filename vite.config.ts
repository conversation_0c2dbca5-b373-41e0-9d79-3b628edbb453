import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "@vant/auto-import-resolver";
// 处理图片的vite插件
import { transformImageUrl } from "./scripts/plugins/transform";
import { delFileByPath } from "./scripts/plugins/delFileByPath";
import { largeImageSrcList } from "./scripts/preBuild/largeImageSrcList";
// 图片远端地址（pc端地址）
const HTTPURLPREFIX = "https://store-test.jiuwei.cloud";

// https://vitejs.dev/config/
export default defineConfig(() => {
  // 判断是否为 H5 平台（通过环境变量或构建目标判断）
  const isH5 = process.env.UNI_PLATFORM === 'h5';

  let plugins: Array<any> = [uni()];

  // 只在 H5 平台加载 Vant(h5端) 相关插件
  if (isH5) {
    plugins.push(
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      })
    );
  }

  if (process.env.NODE_ENV == "production") {
    plugins = [uni()];

    // 只在 H5 平台加载 Vant(h5端) 相关插件
    if (isH5) {
      plugins.push(
        AutoImport({
          resolvers: [VantResolver()],
        }),
        Components({
          resolvers: [VantResolver()],
        })
      );
    }

    // 只在小程序平台加载图片处理插件
    if (!isH5) {
      plugins.push(
        transformImageUrl(largeImageSrcList, HTTPURLPREFIX),
        delFileByPath(largeImageSrcList)
      );
    }
  }

  return {
    plugins,
  };
});
