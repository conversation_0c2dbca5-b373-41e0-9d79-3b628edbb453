import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
// #ifdef H5
// 导入vant
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "@vant/auto-import-resolver";
// #endif
// 处理图片的vite插件
import { transformImageUrl } from "./scripts/plugins/transform";
import { delFileByPath } from "./scripts/plugins/delFileByPath";
import { largeImageSrcList } from "./scripts/preBuild/largeImageSrcList";
// 图片远端地址（pc端地址）
const HTTPURLPREFIX = "https://store-test.jiuwei.cloud";

// https://vitejs.dev/config/
export default defineConfig(() => {
  let plugins: Array<any> = [
    uni(),
    // #ifdef H5
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [VantResolver()],
    }),
    // #endif
  ];
  if (process.env.NODE_ENV == "production") {
    plugins = [
      uni(),
      // #ifdef H5
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
      // #endif
      // #ifdef MP-WEIXIN
      transformImageUrl(largeImageSrcList, HTTPURLPREFIX),
      delFileByPath(largeImageSrcList),
      // #endif
    ];
  }
  return {
    plugins,
  };
});
